plugins {
    id 'java'
    id 'io.freefair.lombok' version '4.1.6'
    id 'org.springframework.boot' version '2.2.13.RELEASE'
    id 'io.spring.dependency-management' version '1.0.9.RELEASE'
    id 'jacoco'
    id 'checkstyle'
    id 'pmd'
}

sourceSets {
    integrationTest {
        java {
            srcDir 'src/integration-test/java'
        }

        compileClasspath += sourceSets.main.output
        compileClasspath += sourceSets.test.output

        runtimeClasspath += sourceSets.main.output
        runtimeClasspath += sourceSets.test.output
    }
}

configurations {
    integrationTestImplementation.extendsFrom testImplementation
}

repositories {
    mavenCentral {
        content {
            excludeGroup 'com.bestseller'
        }
    }

    maven {
        url = 'https://maven.pkg.github.com/bestseller-ecom/bse-commons'
        credentials {
            username githubUsername
            password githubPassword
        }
        content {
            includeGroup 'com.bestseller'
        }
    }
}

dependencyManagement {
    imports {
        mavenBom 'org.springframework.cloud:spring-cloud-dependencies:Hoxton.SR2'
    }
}


// Remove when upgrade to latest Spring boot version!
ext['log4j2.version'] = '2.17.0'

dependencies {
    // Spring Boot
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-configuration-processor'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'

    // Spring Cloud
    implementation 'org.springframework.cloud:spring-cloud-stream'
    implementation 'org.springframework.cloud:spring-cloud-starter-stream-kafka'

    // BSE Commons
    def bseCommonsVersion = '5.1.0'
    implementation "com.bestseller:bse-interface-contracts:${bseCommonsVersion}"

    // Togglz
    implementation platform('org.togglz:togglz-spring-boot:2.6.1.Final')
    implementation 'org.togglz:togglz-spring-boot-starter'
    implementation 'org.togglz:togglz-console'
    implementation 'org.togglz:togglz-spring-security'

    // Shedlock
    def shedlockVersion = '4.0.0'
    implementation "net.javacrumbs.shedlock:shedlock-spring:${shedlockVersion}"
    implementation "net.javacrumbs.shedlock:shedlock-provider-jdbc-template:${shedlockVersion}"

    implementation 'mysql:mysql-connector-java:8.0.17'
    implementation 'org.flywaydb:flyway-core:5.2.4'
    implementation 'org.modelmapper:modelmapper:2.3.6'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.9'
    implementation 'org.apache.commons:commons-lang3:3.9'
    implementation 'javax.validation:validation-api:2.0.1.Final'
    implementation 'org.jeasy:easy-rules-core:3.4.0'
    implementation 'org.jeasy:easy-rules-support:3.4.0'
    implementation 'com.github.npathai:hamcrest-optional:2.0.0'
    implementation 'org.apache.tomcat:tomcat-jdbc:9.0.24'
    implementation "net.logstash.logback:logstash-logback-encoder:6.1"

    // Datadog
    def micrometerVersion = "1.6.4"
    implementation "io.micrometer:micrometer-core:${micrometerVersion}"
    implementation "io.micrometer:micrometer-registry-datadog:${micrometerVersion}"

    def ddVersion = "0.104.0"
    implementation "com.datadoghq:dd-trace-api:${ddVersion}"
    runtimeOnly "com.datadoghq:dd-java-agent:${ddVersion}"

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.awaitility:awaitility:4.0.1'
    testImplementation 'org.mockito:mockito-inline:2.23.4'
}

group = 'com.bestseller.services.orderrouting'
version = '1.0-SNAPSHOT'
description = 'Order Routing Service'
sourceCompatibility = '13'

configurations.all {
    exclude(group: 'commons-logging')
}

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

checkstyle {
    sourceSets = [sourceSets.main, sourceSets.test]
}

pmd {
    ruleSets = []
}

pmdMain {
    ruleSetFiles = files 'config/pmd/main.xml'
}

pmdTest {
    ruleSetFiles = files 'config/pmd/test.xml'
    ignoreFailures = true
}

pmdIntegrationTest {
    ruleSetFiles = files 'config/pmd/test.xml'
    ignoreFailures = true
}

pmdMain.exclude '**/model/*.*', '**/messaging/validation/*.*'

task integrationTest(type: Test) {
    description = 'Runs integration tests.'
    group = 'verification'

    testClassesDirs = sourceSets.integrationTest.output.classesDirs
    classpath = sourceSets.integrationTest.runtimeClasspath
    shouldRunAfter test

    environment['spring.profiles.active'] = 'dev'
}

jacocoTestCoverageVerification {
    violationRules {
        rule {
            element = 'CLASS'
            limit {
                counter = 'LINE'
                value = 'COVEREDRATIO'
                minimum = 1.****************
                excludes = [
                    'com.bestseller.services.orderrouting.Application',
                    'com.bestseller.services.orderrouting.event.handler.OrderPartsRoutedHandler',
                    '*.configuration.*',
                    '*Exception',
                    "*Operation",
                    '*Rule',
                ]
            }
        }
    }
}

check {
    dependsOn integrationTest
    finalizedBy jacocoTestReport
}

task copyDatadogJavaAgent(type: Copy) {
    from configurations.runtimeClasspath
    include "dd-java-agent-*.jar"
    into project.libsDirectory
}

assemble.dependsOn copyDatadogJavaAgent

bootRun {
    args = ['--spring.profiles.active=dev']
}
