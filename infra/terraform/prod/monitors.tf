locals {
  application_name   = lower(replace(local.project_name, " ", "_"))
  escalation_message = ""

  monitor_cpu = {
    name = "CPU utilization crossed threshold"
    thresholds = {
      ok                = 0.5
      warning_recovery  = 0.7
      warning           = 0.8
      critical_recovery = 0.8
      critical          = 0.9
    }
    new_host_delay = 300
  }

  monitor_memory = {
    name = "Memory deviates from expected pattern"
    thresholds = {
      ok                = 0
      warning_recovery  = 0.25
      warning           = 0.5
      critical_recovery = 0.5
      critical          = 0.8
    }
    new_host_delay = 300
  }

  error_count = {
    name = "Service is producing errors"
    thresholds = {
      ok                = 0
      warning_recovery  = 0
      warning           = 1
      critical_recovery = 1
      critical          = 10
    }
  }

  host = {
    name = "Host is down"
    thresholds = {
      ok                = 1
      warning_recovery  = 0.8
      warning           = 0.5
      critical_recovery = 1
      critical          = 0
    }
    new_host_delay    = 300
    no_data_timeframe = 10
  }

  message_lag = {
    name           = "Kafka lag increased outside the threshold"
    consumer_group = lower(local.consumer_group)
    thresholds = {
      ok                = 30
      warning_recovery  = 50
      warning           = 55
      critical_recovery = 65
      critical          = 70
    }
  }

  message_high_priority = <<EOF
{{#is_alert}}
  Status is CRITICAL on ${local.env}. Value {{ value }} is {{ comparator }} than {{ threshold }}
  @webhook-LTS_P1
{{/is_alert}}

{{#is_recovery}}
  Status is NORMAL on ${local.env}. Current value is {{ value }}
{{/is_recovery}}

{{#is_warning}}
  Status is WARNING on ${local.env}. Value {{ value }} is {{ comparator }} than {{ warn_threshold }}
  @webhook-LTS_P2
{{/is_warning}}

{{#is_warning_recovery}}
  Status is NORMAL on ${local.env}. Current valus is {{ value }}
{{/is_warning_recovery}}

EOF

  message_medium_priority = <<EOF
{{#is_alert}}
  Status is CRITICAL on ${local.env}. Value {{ value }} is {{ comparator }} than {{ threshold }}
  @webhook-LTS_P2
{{/is_alert}}

{{#is_recovery}}
  Status is NORMAL on ${local.env}. Current value is {{ value }}
{{/is_recovery}}

{{#is_warning}}
  Status is WARNING on ${local.env}. Value {{ value }} is {{ comparator }} than {{ warn_threshold }}
  @webhook-LTS_P3
{{/is_warning}}

{{#is_warning_recovery}}
  Status is NORMAL on ${local.env}. Current valus is {{ value }}
{{/is_warning_recovery}}

EOF

}

module "ors_cpu_monitor" {
  source             = "tfe.mng.bseint.io/bestseller-ecom/monitor-metric-alert/datadog"
  version            = "~> 3.1.0"
  vcs                = local.vcs
  env                = local.env
  name               = local.monitor_cpu.name
  owner              = local.owner
  project            = local.project
  query              = "min(last_30m):avg:system.cpu.usage{service:${local.application_name},env:${local.env}} >= ${local.monitor_cpu.thresholds.critical}"
  threshold_warning  = local.monitor_cpu.thresholds.warning
  threshold_critical = local.monitor_cpu.thresholds.critical
  threshold_ok       = local.monitor_cpu.thresholds.ok
  critical_recovery  = local.monitor_cpu.thresholds.critical_recovery
  warning_recovery   = local.monitor_cpu.thresholds.warning_recovery
  message            = local.message_medium_priority
  escalation_message = local.escalation_message
}

module "ors_memory_monitor" {
  source             = "tfe.mng.bseint.io/bestseller-ecom/monitor-metric-alert/datadog"
  version            = "~> 3.1.0"
  vcs                = local.vcs
  env                = local.env
  name               = local.monitor_memory.name
  owner              = local.owner
  project            = local.project
  query              = "avg(last_12h):anomalies(avg:jvm.memory.used{service:${local.application_name},env:${local.env}}, 'agile', 2, direction='above', alert_window='last_30m', interval=120, count_default_zero='true', seasonality='hourly') >= 0.8"
  threshold_warning  = local.monitor_memory.thresholds.warning
  threshold_critical = local.monitor_memory.thresholds.critical
  threshold_ok       = local.monitor_memory.thresholds.ok
  critical_recovery  = local.monitor_memory.thresholds.critical_recovery
  warning_recovery   = local.monitor_memory.thresholds.warning_recovery
  message            = local.message_medium_priority
  escalation_message = local.escalation_message
}

module "ors_error_count_monitor" {
  source             = "tfe.mng.bseint.io/bestseller-ecom/monitor-log-alert/datadog"
  version            = "~> 2.0.0"
  env                = local.env
  name               = local.error_count.name
  owner              = local.owner
  project            = local.project
  query              = <<-EOT
                        logs("service:${local.project} status:error env:${local.env}  -\"Picked up JAVA_TOOL_OPTIONS\"").index("main").rollup("count").last("10m") >= ${local.error_count.thresholds.critical}
  EOT
  threshold_ok       = local.error_count.thresholds.ok
  threshold_warning  = local.error_count.thresholds.warning
  threshold_critical = local.error_count.thresholds.critical
  message            = local.message_medium_priority
  escalation_message = local.escalation_message
}

module "ors_availability_monitor" {
  source             = "tfe.mng.bseint.io/bestseller-ecom/monitor-metric-alert/datadog"
  version            = "~> 3.1.0"
  vcs                = local.vcs
  env                = local.env
  name               = local.host.name
  owner              = local.owner
  project            = local.project
  query              = "min(last_15m):avg:aws.ecs.service.running{service:${local.project}-service-prod} <= 0"
  threshold_warning  = local.host.thresholds.warning
  threshold_critical = local.host.thresholds.critical
  threshold_ok       = local.host.thresholds.ok
  critical_recovery  = local.host.thresholds.critical_recovery
  warning_recovery   = local.host.thresholds.warning_recovery
  message            = local.message_high_priority
  escalation_message = local.escalation_message
  no_data_timeframe  = local.host.no_data_timeframe
}

module "ors_message_lag_monitor" {
  source             = "tfe.mng.bseint.io/bestseller-ecom/monitor-metric-alert/datadog"
  version            = "~> 3.1.0"
  vcs                = local.vcs
  env                = local.env
  name               = local.message_lag.name
  owner              = local.owner
  project            = local.project
  query              = "min(last_30m):avg:kafka.consumer_lag{consumer_group:${local.message_lag.consumer_group},env:${local.env}} > ${local.message_lag.thresholds.critical}"
  threshold_warning  = local.message_lag.thresholds.warning
  threshold_critical = local.message_lag.thresholds.critical
  threshold_ok       = local.message_lag.thresholds.ok
  critical_recovery  = local.message_lag.thresholds.critical_recovery
  warning_recovery   = local.message_lag.thresholds.warning_recovery
  message            = local.message_medium_priority
  escalation_message = local.escalation_message
}
