package com.bestseller.services.orderrouting.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Address model to persist address information in the Aurora.
 */
@Data
@Entity
@Table(name = "Address")
public class AddressModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String addressLine1;

    private String addressLine2;

    private String addressLine3;

    private String city;

    private String country;

    private String state;

    private String firstName;

    private String houseNumber;

    private String houseNumberExtended;

    private String lastName;

    private String phoneNumber;

    private String zipcode;

    private String storeId;
}
