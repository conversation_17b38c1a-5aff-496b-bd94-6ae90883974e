 package com.bestseller.services.orderrouting.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Reference;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * Order model to persist order information in the Aurora.
 */
@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "\"Order\"")
public class OrderModel {

    @Id
    private String orderId;

    private Instant placedDate;

    private Boolean isTest = false;

    private Boolean isOrderAdvice = false;

    private Boolean holdFromRouting;

    private String holdFromRoutingNode;

    private String customerEmail;

    private String externalCustomerNumber;

    @OneToOne(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinColumn(name = "billingAddressId", referencedColumnName = "id")
    private AddressModel billingAddress;

    @OneToOne(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinColumn(name = "shippingAddressId", referencedColumnName = "id")
    private AddressModel shippingAddress;

    private BigDecimal orderValue;

    private BigDecimal shippingFees;

    private BigDecimal shippingFeesTaxPercentage;

    private String orderType;

    private String shippingMethod;

    private String externalOrderNumber;

    private String carrier;

    private String carrierVariant;

    private Instant orderCreationDate;

    private String customerId;

    private Boolean shippingFeesCancelled = false;

    private String marketPlace;

    private String store;

    private String platform;

    private String channel;

    private String brand;

    private String actionCode;

    private String customerLocale;

    private String currency;

    private String checkout;

    @Reference
    @OneToMany(mappedBy = "orderId", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<OrderLineQuantityModel> orderLineQuantities = new ArrayList<>();

    @OneToMany(mappedBy = "orderId", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PaymentModel> payments = new ArrayList<>();

    private String parcelLocker;

    private Instant createdDate = Instant.now();

    @OneToMany(mappedBy = "orderId", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<AdditionalOrderInformationModel> additionalOrderInformation = new ArrayList<>();

    private String isoStoreId;

    private Boolean isReturnTrackingNumberRequired;
}
