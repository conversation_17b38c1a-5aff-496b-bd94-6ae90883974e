package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.model.AdditionalOrderInformationModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.model.PaymentModel;
import com.bestseller.services.orderrouting.service.EntityType;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeMap;
import org.modelmapper.TypeToken;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * Implementation of {@link Converter} which converts {@link OrderForFulfillment} to {@link OrderModel}.
 * Uses {@link ModelMapper}, {@link TypeMap} and {@link MatchingStrategies} from ModelMapper Java library.
 *
 * @see Converter
 * @see ModelMapper
 * @see TypeMap
 * @see MatchingStrategies
 */
@Component
public class OrderConverter implements Converter<OrderForFulfillment, OrderModel> {

    private static final org.modelmapper.Converter<ZonedDateTime, Instant> DATE_CONVERTER = cxt ->
            cxt.getSource() == null ? null : cxt.getSource().toInstant();

    private final ModelMapper modelMapper = createModelMapper();

    /**
     * {@inheritDoc}
     */
    @Override
    public OrderModel convert(OrderForFulfillment orderMessage) {
        OrderModel orderModel = modelMapper.map(orderMessage, OrderModel.class);

        for (OrderLineQuantityModel orderLineQuantity : orderModel.getOrderLineQuantities()) {
            orderLineQuantity.setOrderId(orderModel.getOrderId());
        }

        for (PaymentModel payment : orderModel.getPayments()) {
            payment.setOrderId(orderModel.getOrderId());
        }

        for (AdditionalOrderInformationModel additionalInformation : orderModel.getAdditionalOrderInformation()) {
            additionalInformation.setEntityType(EntityType.SHIPPING_INFORMATION);
            additionalInformation.setOrderId(orderModel.getOrderId());
        }

        return orderModel;
    }

    private static ModelMapper createModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT);

        // Add skipping for setting order creation date for the order because there were problems with lombok smart matching;
        // i.e. whenever createdDate was null it was assigned orderCreationDate value instead
        TypeMap<OrderForFulfillment, OrderModel> orderForFulfillmentTypeMap =
                modelMapper.createTypeMap(OrderForFulfillment.class, OrderModel.class);
        orderForFulfillmentTypeMap.addMappings(mapper -> {
            mapper.skip(OrderModel::setCreatedDate);
            mapper.skip(OrderModel::setIsOrderAdvice);
        });

        orderForFulfillmentTypeMap.addMapping(src -> src.getFulfillmentAdvice().getFulfillmentNode(), OrderModel::setHoldFromRoutingNode);
        orderForFulfillmentTypeMap.addMapping(src -> src.getFulfillmentAdvice().getHoldFromRouting(), OrderModel::setHoldFromRouting);
        orderForFulfillmentTypeMap.addMapping(src -> src.getShippingInformation().getShippingAddress(), OrderModel::setShippingAddress);
        orderForFulfillmentTypeMap.addMapping(src -> src.getShippingInformation().getParcelLocker(), OrderModel::setParcelLocker);
        orderForFulfillmentTypeMap.addMapping(src -> src.getShippingInformation().getIsReturnTrackingNumberRequired(),
            OrderModel::setIsReturnTrackingNumberRequired);
        orderForFulfillmentTypeMap.addMapping(src ->
            src.getShippingInformation().getAdditionalInformation(), OrderModel::setAdditionalOrderInformation);
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderLines(), OrderModel::setOrderLineQuantities);
        mapOrderDetails(orderForFulfillmentTypeMap);
        mapCustomerInformation(orderForFulfillmentTypeMap);
        orderForFulfillmentTypeMap.addMappings(mapper -> {
            mapper.using(DATE_CONVERTER).map(OrderForFulfillment::getPlacedDate, OrderModel::setPlacedDate);
            mapper.using(DATE_CONVERTER).map(order -> order.getOrderDetails().getOrderCreationDate(),
                    OrderModel::setOrderCreationDate);
        });

        // Add skipping for setting internal id and order part number for the order line.
        TypeMap<OrderLine, OrderLineQuantityModel> orderLineTypeMap =
                modelMapper.createTypeMap(OrderLine.class, OrderLineQuantityModel.class);

        orderLineTypeMap.addMappings(mapper -> {
            mapper.skip(OrderLineQuantityModel::setId);
            mapper.skip(OrderLineQuantityModel::setOrderPartNumber);
        });

        return modelMapper;
    }

    private static void mapCustomerInformation(TypeMap<OrderForFulfillment, OrderModel> orderForFulfillmentTypeMap) {
        orderForFulfillmentTypeMap.addMapping(src -> src.getCustomerInformation().getBillingAddress(), OrderModel::setBillingAddress);
        orderForFulfillmentTypeMap.addMapping(src -> src.getCustomerInformation().getEmail(), OrderModel::setCustomerEmail);
        orderForFulfillmentTypeMap.addMapping(src -> src.getCustomerInformation().getCustomerId(), OrderModel::setCustomerId);
        orderForFulfillmentTypeMap.addMapping(src -> src.getCustomerInformation().getExternalCustomerNumber(), OrderModel::setExternalCustomerNumber);
        orderForFulfillmentTypeMap.addMapping(src -> src.getCustomerInformation().getCustomerLocale(), OrderModel::setCustomerLocale);
    }

    private static void mapOrderDetails(TypeMap<OrderForFulfillment, OrderModel> orderForFulfillmentTypeMap) {
        // due to ambiguity in parcelShop and parcelShopType we need to explicitly set one of both
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderDetails().getOrderValue(), OrderModel::setOrderValue);
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderDetails().getShippingFees(), OrderModel::setShippingFees);
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderDetails().getShippingFeesTaxPercentage(), OrderModel::setShippingFeesTaxPercentage);
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderDetails().getOrderType(), OrderModel::setOrderType);
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderDetails().getShippingMethod(), OrderModel::setShippingMethod);
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderDetails().getCarrier(), OrderModel::setCarrier);
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderDetails().getCarrierVariant(), OrderModel::setCarrierVariant);
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderDetails().getExternalOrderNumber(), OrderModel::setExternalOrderNumber);
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderDetails().getCurrency(), OrderModel::setCurrency);
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderDetails().getCheckout(), OrderModel::setCheckout);
        orderForFulfillmentTypeMap.addMapping(src -> src.getOrderDetails().getIsoStoreId(), OrderModel::setIsoStoreId);
    }

    /**
     * Converts orderPartsRouted orderLine to orderLineQuantityModel.
     *
     * @param orderLines orderLines of orderPartsRouted
     * @return orderLineQuantityModels
     */
    public List<OrderLineQuantityModel> convertRoutedLines(
            List<OrderLine> orderLines) {
        return convertLines(orderLines);
    }

    private List<OrderLineQuantityModel> convertLines(List<?> orderLines) {
        return modelMapper.map(orderLines, new TypeToken<List<OrderLineQuantityModel>>() { }.getType());
    }

    /**
     * Makes deep copy of orderModel.
     *
     * @param orderModel  original object
     * @return orderModel deep copy
     */

    public OrderModel deepCopyOrderModel(OrderModel orderModel) {
        OrderModel copy = new OrderModel();
        modelMapper.map(orderModel, copy, "");
        return copy;
    }

    /**
     * Makes deep copy of orderLineQuantityModel.
     *
     * @param orderLineQuantityModel original object
     * @return orderLineQuantityModel deep copy
     */

    public OrderLineQuantityModel deepCopyOrderQuantityModel(OrderLineQuantityModel orderLineQuantityModel) {
        OrderLineQuantityModel copy = new OrderLineQuantityModel();
        modelMapper.map(orderLineQuantityModel, copy, "");
        return copy;
    }
}
