package com.bestseller.services.orderrouting.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.configuration.messaging.OrderChannels;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.messaging.exceptions.IdempotentMessageException;
import com.bestseller.services.orderrouting.messaging.exceptions.ProcessingException;
import com.bestseller.services.orderrouting.messaging.validation.ValidationException;
import com.bestseller.services.orderrouting.messaging.validation.Validator;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.service.RulesService;
import com.bestseller.services.orderrouting.service.idempotency.MessageIdempotencyService;
import datadog.trace.api.Trace;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.api.Facts;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.text.MessageFormat;

import static com.bestseller.services.orderrouting.metric.CounterOperation.ORDER_FOR_FULFILLMENT_CONSUMED;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_FOR_FULFILLMENT_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

/**
 * Consumer listening for {@link OrderForFulfillment} message on 'OrderForFulfillment' input channel.
 */
@Component
@AllArgsConstructor
@Validated
@Slf4j
public class OrderForFulfillmentMessageConsumer {

    private OrderService orderService;
    private Validator<OrderForFulfillment> orderFulfillmentMessageValidator;
    private MessageIdempotencyService<OrderForFulfillment> messageIdempotencyService;
    private final Converter<OrderForFulfillment, OrderModel> orderConverter;
    private final RulesService rulesService;
    private final MeterRegistry meterRegistry;

    /**
     * The method that consume messages sent through {@link OrderChannels}.
     *
     * @param orderForFulfillment
     */
    @Transactional
    @StreamListener(OrderChannels.ORDER_FOR_FULFILLMENT)
    @Trace(operationName = OrderChannels.ORDER_FOR_FULFILLMENT)
    public void receiveOrderForFulfillment(@NotNull @Valid OrderForFulfillment orderForFulfillment)
            throws ValidationException, ProcessingException {
        try {
            OrderModel orderModel = processOrderForFulfillment(orderForFulfillment);
            placeOrder(orderForFulfillment, orderModel);
            meterRegistry.counter(ORDER_FOR_FULFILLMENT_CONSUMED).increment();
        } catch (IdempotentMessageException e) {
            log.warn("This OrderForFulfillment message for order with id {} has been processed before!",
                    orderForFulfillment.getOrderId());
        } catch (MessagingException e) {
            String orderId = orderForFulfillment.getOrderId();
            orderService.deleteOrderModel(orderService.findOrderModel(orderId));
            throw new ProcessingException(
                    MessageFormat.format("Unable to publish message ItemAvailabilityRequest with order id: {0}", orderId), e);
        } catch (TransientDataAccessException e) {
            // skipping stack trace here
            log.warn("Order fulfillment needs to be retried: {}", orderForFulfillment.getOrderId());

            // this particular stack trace is large enough to break Datadog's JSON parser, but is nevertheless helpful to have logged
            log.warn("Order fulfillment needs to be retried: {} (including stack trace of {} frames)",
                    orderForFulfillment.getOrderId(), e.getStackTrace().length, e);

            throw e;
        }
    }

    private OrderModel processOrderForFulfillment(final OrderForFulfillment orderForFulfillment) throws ValidationException,
            IdempotentMessageException {
        log.debug("OrderForFulfillment message received. Starting validation...");

        // Validate the message.
        orderFulfillmentMessageValidator.validate(orderForFulfillment);

        // Check for message idempotency and return 'true' if the message is duplicate
        // And throws exception in case its a duplicate message
        if (messageIdempotencyService.checkDuplicateMessage(orderForFulfillment)) {
            IdempotentMessageException e = new IdempotentMessageException();
            if (ORSFeatures.ORDER_FOR_FULFILLMENT_IDEMPOTENCY_CHECK_REQUIRED.isActive()) {
                throw e;
            } else {
                log.error("Idempotency Check for orderPartsRouted message error was ignored", e);
            }
        }

        log.debug("OrderForFulfillment message is valid. Saving to database message: \"{}\".",
                orderForFulfillment.toString());

        // Save the message through OrderService.
        OrderModel orderModel = orderConverter.convert(orderForFulfillment);
        orderModel = orderService.saveOrderModel(orderModel);

        log.debug("Order is saved to database. Requesting availability for the items.");

        return orderModel;
    }

    private void placeOrder(OrderForFulfillment orderForFulfillment, OrderModel orderModel) {
        final Facts facts = getPlacingRulesFacts(orderForFulfillment, orderModel);

        rulesService.executeOrderPlacingRules(facts);
    }

    private Facts getPlacingRulesFacts(OrderForFulfillment orderForFulfillment, OrderModel orderModel) {
        final Facts facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);
        facts.put(ORDER_FOR_FULFILLMENT_FACT, orderForFulfillment);

        return facts;
    }
}
