package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.model.AdditionalOrderInformationModel;
import com.bestseller.services.orderrouting.model.AddressModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.model.PaymentModel;
import com.bestseller.services.orderrouting.service.EntityType;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.Arrays.asList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.hasProperty;
import static org.hamcrest.Matchers.hasSize;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class OrderPartsRoutedConverterTest {
    private static final String WAREHOUSE_1 = "WAREHOUSE-1";
    private static final String WAREHOUSE_2 = "WAREHOUSE-2";
    private static final int ORDER_PART_NUMBER_1 = 1;
    private static final int ORDER_PART_NUMBER_2 = 2;
    private static final String SIZE_SHOULD_BE_2 = "Size should be 2";
    private static final String ISO_STORE_ID = "781263";

    @InjectMocks
    private OrderPartsRoutedConverter converter;

    private OrderModel orderModel;
    private OrderLineQuantityModel orderLine1;
    private OrderLineQuantityModel orderLine2;
    private OrderLineQuantityModel orderLine3;

    @Before
    public void setUp() {
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setIsoStoreId(ISO_STORE_ID);
        orderModel.setPayments(OrderModelGenerator.createTwoPayments(orderModel));
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));
        orderModel.getOrderLineQuantities().forEach(q -> q.setStatus(OrderLineQuantityStatus.ROUTING));
        orderModel.setIsReturnTrackingNumberRequired(Boolean.TRUE);
        orderLine1 = orderModel.getOrderLineQuantities().get(0);
        orderLine2 = orderModel.getOrderLineQuantities().get(1);
        orderLine3 = orderModel.getOrderLineQuantities().get(2);

        orderLine1.setFulfillmentNode(WAREHOUSE_1);
        orderLine1.setOrderPartNumber(1);
        orderLine2.setFulfillmentNode(WAREHOUSE_1);
        orderLine2.setOrderPartNumber(1);
        orderLine3.setFulfillmentNode(WAREHOUSE_2);
        orderLine3.setOrderPartNumber(2);
    }

    @Test
    public void convert_noRoutingLinesGiven_noOrderPartMessages() {
        // arrange
        orderModel.getOrderLineQuantities().forEach(o -> o.setStatus(OrderLineQuantityStatus.ROUTED));

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertEquals("Size should be zero", 0, orderParts.size());
    }

    @Test
    public void convert_routingAndRoutedLinesGiven_orderPartMessagesCreated() {
        // arrange
        OrderLineQuantityModel orderLinePlaced = new OrderLineQuantityModel();
        orderLinePlaced.setStatus(OrderLineQuantityStatus.ROUTED);
        orderLinePlaced.setOrderPartNumber(1);
        orderModel.getOrderLineQuantities().add(orderLinePlaced);

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertEquals(SIZE_SHOULD_BE_2, 2, orderParts.size());
        assertOrderPartMessage(orderParts.get(0), WAREHOUSE_1, ORDER_PART_NUMBER_1, orderLine1, orderLine2);
        assertOrderPartMessage(orderParts.get(1), WAREHOUSE_2, ORDER_PART_NUMBER_2, orderLine3);
    }

    @Test
    public void convert_resubmitLinesGiven_orderPartMessagesCreated() {
        // arrange
        OrderLineQuantityModel orderLinePlaced = new OrderLineQuantityModel();
        orderLinePlaced.setStatus(OrderLineQuantityStatus.RESUBMIT);
        orderLinePlaced.setOrderPartNumber(1);
        orderModel.getOrderLineQuantities().add(orderLinePlaced);

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertEquals(SIZE_SHOULD_BE_2, 2, orderParts.size());
        assertOrderPartMessage(orderParts.get(0), WAREHOUSE_1, ORDER_PART_NUMBER_1, orderLine1, orderLine2,
                               orderLinePlaced);
        assertOrderPartMessage(orderParts.get(1), WAREHOUSE_2, ORDER_PART_NUMBER_2, orderLine3);
    }

    @Test
    public void convert_routingLinesGiven_orderPartMessagesCreated() {
        // arrange

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertEquals(SIZE_SHOULD_BE_2, 2, orderParts.size());
        assertThat("Order parts value should be set", orderParts, everyItem(hasProperty("totalOrderParts",
                                                                                        equalTo(2))));
        assertOrderPartMessage(orderParts.get(0), WAREHOUSE_1, ORDER_PART_NUMBER_1,
                orderLine1, orderLine2);
        assertOrderPartMessage(orderParts.get(1), WAREHOUSE_2, ORDER_PART_NUMBER_2,
                orderLine3);
    }

    @Test
    public void convert_singleLine_onePartWithCorrectNumbers() {
        // arrange
        OrderLineQuantityModel line = OrderModelGenerator.createOneTestOrderLine(orderModel);
        line.setStatus(OrderLineQuantityStatus.ROUTING);
        line.setOrderPartNumber(1);
        orderModel.setOrderLineQuantities(asList(line));

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertThat("Size should be 1", orderParts, hasSize(1));
        assertThat("Total order parts should be 1", orderParts.get(0).getTotalOrderParts(), equalTo(1));
        assertThat("Order part number should be 1", orderParts.get(0).getOrderPartNumber(),
                   equalTo(ORDER_PART_NUMBER_1));
    }

    @Test
    public void convert_nullInstancesGiven_nullZonedDateTimesSet() {
        // arrange
        orderModel.setPlacedDate(null);
        orderModel.setOrderCreationDate(null);

        // act
        List<OrderPartsRouted> orderParts = converter.convert(orderModel);

        // assert
        assertEquals(SIZE_SHOULD_BE_2, 2, orderParts.size());
        assertEquals("Placed date should be null", asList(null, null), orderParts
                .stream()
                .map(OrderPartsRouted::getPlacedDate)
                .collect(Collectors.toList()));
        assertEquals("Order creation date should be null", asList(null, null), orderParts
                .stream()
                .map(OrderPartsRouted::getOrderDetails)
                .map(OrderDetails::getOrderCreationDate)
                .collect(Collectors.toList()));
    }

    @SuppressWarnings("PMD.NcssCount")
    private void assertOrderPartMessage(OrderPartsRouted message, String expectedFulfillmentNode,
                                        Integer expectedOrderPartNumber, OrderLineQuantityModel... lines) {
        assertEquals("Order id should match",
                orderModel.getOrderId(), message.getOrderId());
        assertEquals("Fulfillment node should match",
                expectedFulfillmentNode, message.getFulfillmentNode());
        assertEquals("Order part number should match",
                expectedOrderPartNumber, message.getOrderPartNumber());

        assertEquals("Marketplace should match",
                orderModel.getMarketPlace(), message.getMarketPlace());
        assertEquals("Store should match",
                orderModel.getStore(), message.getStore());
        assertEquals("Channel should match",
                orderModel.getChannel(), message.getChannel());
        assertEquals("Brand should match",
                orderModel.getBrand(), message.getBrand());
        assertEquals("Action code should match",
                orderModel.getActionCode(), message.getActionCode());
        assertEquals("Carrier should match",
                orderModel.getCarrier(), message.getOrderDetails().getCarrier());
        assertEquals("Carrier variant should match",
                orderModel.getCarrierVariant(), message.getOrderDetails().getCarrierVariant());
        assertEquals("Customer email should match",
                orderModel.getCustomerEmail(), message.getCustomerInformation().getEmail());
        assertEquals("Customer id should match",
                orderModel.getCustomerId(), message.getCustomerInformation().getCustomerId());
        assertEquals("Customer locale should match",
                     orderModel.getCustomerLocale(), message.getCustomerInformation().getCustomerLocale());
        assertEquals("External customer number",
                orderModel.getExternalCustomerNumber(), message.getCustomerInformation().getExternalCustomerNumber());
        assertEquals("External order number should match",
                orderModel.getExternalOrderNumber(), message.getOrderDetails().getExternalOrderNumber());
        assertEquals("IsTest should match",
                orderModel.getIsTest(), message.getIsTest());
        assertEquals("Creation date should match",
                orderModel.getOrderCreationDate(), message.getOrderDetails().getOrderCreationDate().toInstant());
        assertEquals("Order type should match",
                orderModel.getOrderType(), message.getOrderDetails().getOrderType());
        assertEquals("Order value should match",
                orderModel.getOrderValue(), message.getOrderDetails().getOrderValue());
        assertEquals("Order currency should match",
                orderModel.getCurrency(), message.getOrderDetails().getCurrency());
        assertEquals("Order checkout should match",
                orderModel.getCheckout(), message.getOrderDetails().getCheckout());
        assertEquals("Placed date should match",
                orderModel.getPlacedDate(), message.getPlacedDate().toInstant());
        assertEquals("Shipping fees should match",
                orderModel.getShippingFees(), message.getOrderDetails().getShippingFees());
        assertEquals("Shipping fees cancelled should match",
                orderModel.getShippingFeesCancelled(), message.getOrderDetails().getShippingFeesCancelled());
        assertEquals("Shipping fees tax percentage should match",
                orderModel.getShippingFeesTaxPercentage(), message.getOrderDetails().getShippingFeesTaxPercentage());
        assertEquals("IsoStoreId should match",
            orderModel.getIsoStoreId(), message.getOrderDetails().getIsoStoreId());

        assertAddress(orderModel.getBillingAddress(), message.getCustomerInformation().getBillingAddress(),
                "billing");
        assertAddress(orderModel.getShippingAddress(), message.getShippingInformation().getShippingAddress(),
                "shipping");
        assertEquals("Parcel locker should match",
                orderModel.getParcelLocker(), message.getShippingInformation().getParcelLocker());
        assertEquals("IsReturnTrackingNumberRequired should match",
                orderModel.getIsReturnTrackingNumberRequired(), message.getShippingInformation().getIsReturnTrackingNumberRequired());

        assertAdditionalInformationSet(orderModel.getAdditionalOrderInformation(), message.getShippingInformation().getAdditionalInformation());

        assertEquals("Lines size should match", lines.length, message.getOrderLines().size());
        for (int i = 0; i < lines.length; i++) {
            OrderLine orderLine = message.getOrderLines().get(i);
            assertEquals("EAN should match for line " + i,
                    lines[i].getEan(), orderLine.getEan());
            assertEquals("Product name should match for line " + i,
                    lines[i].getProductName(), orderLine.getProductName());
            assertEquals("Line number should match for line " + i,
                    lines[i].getLineNumber(), orderLine.getLineNumber());
            assertEquals("Quantity should match for line " + i,
                    lines[i].getQuantity(), orderLine.getQuantity());
            assertEquals("Retail price should match for line " + i,
                    lines[i].getRetailPrice(), orderLine.getRetailPrice());
            assertEquals("Discount value should match for line " + i,
                    lines[i].getDiscountValue(), orderLine.getDiscountValue());
            assertEquals("Tax percentage should match for line " + i,
                    lines[i].getTaxPercentage(), orderLine.getTaxPercentage());
            assertEquals("Is gift item should match for line " + i,
                    lines[i].getIsGiftItem(), orderLine.getIsGiftItem());
            assertEquals("Partner reference should match for line " + i,
                    lines[i].getPartnerReference(), orderLine.getPartnerReference());
        }

        assertEquals("Payments size should match", orderModel.getPayments().size(), message.getPayments().size());
        Iterator<PaymentModel> paymentModelIterator = orderModel.getPayments().iterator();
        Iterator<Payment> paymentIterator = message.getPayments().iterator();
        int i = 1;
        while (paymentModelIterator.hasNext()) {
            PaymentModel expected = paymentModelIterator.next();
            Payment actual = paymentIterator.next();
            assertEquals("Payment type should match for payment line " + i, expected.getName(), actual.getName());
            assertEquals("Payment method should match for payment line " + i, expected.getSubMethod(), actual.getSubMethod());
            i++;
        }
    }

    private void assertAddress(AddressModel expected, Address actual, String addressType) {
        assertEquals("Address line 1 should match for address type " + addressType,
                expected.getAddressLine1(), actual.getAddressLine1());
        assertEquals("Address line 2 should match for address type " + addressType,
                expected.getAddressLine2(), actual.getAddressLine2());
        assertEquals("Address line 3 should match for address type " + addressType,
                expected.getAddressLine3(), actual.getAddressLine3());
        assertEquals("City should match for address type " + addressType,
                expected.getCity(), actual.getCity());
        assertEquals("Country should match for address type " + addressType,
                expected.getCountry(), actual.getCountry());
        assertEquals("State should match for address type " + addressType,
                expected.getState(), actual.getState());
        assertEquals("First name should match for address type " + addressType,
                expected.getFirstName(), actual.getFirstName());
        assertEquals("Last name should match for address type " + addressType,
                expected.getLastName(), actual.getLastName());
        assertEquals("House number should match for address type " + addressType,
                expected.getHouseNumber(), actual.getHouseNumber());
        assertEquals("House number extended should match for address type " + addressType,
                expected.getHouseNumberExtended(), actual.getHouseNumberExtended());
        assertEquals("Phone should match for address type " + addressType,
                expected.getPhoneNumber(), actual.getPhoneNumber());
        assertEquals("Zip should match for type " + addressType,
                expected.getZipcode(), actual.getZipcode());
    }

    private void assertAdditionalInformationSet(List<AdditionalOrderInformationModel> additionalInformationList,
                                                  Set<AdditionalInformation> additionalInformationModelSet) {
        assertEquals("Size should match", additionalInformationList.size(), additionalInformationModelSet.size());
        Iterator<AdditionalOrderInformationModel> additionalInformationIterator = additionalInformationList.iterator();
        Iterator<AdditionalInformation> additionalInformationModelIterator = additionalInformationModelSet.iterator();
        while (additionalInformationIterator.hasNext()) {
            assertAdditionalInformation(additionalInformationModelIterator.next(), additionalInformationIterator.next());
        }
    }

    private void assertAdditionalInformation(
        AdditionalInformation additionalInformation,
        AdditionalOrderInformationModel additionalOrderInformationModel) {
        assertEquals("Entity type should match", EntityType.SHIPPING_INFORMATION, additionalOrderInformationModel.getEntityType());
        assertEquals("Key should match", additionalInformation.getKey(), additionalOrderInformationModel.getKey());
        assertEquals("Value should match", additionalInformation.getValue(), additionalOrderInformationModel.getValue());
    }
}
