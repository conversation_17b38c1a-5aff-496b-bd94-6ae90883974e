package com.bestseller.services.orderrouting.utils.generator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * Generates {@link OrderForFulfillment} message for test purposes.
 */
@SuppressWarnings("PMD.ClassNamingConventions")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OrderForFulfillmentGenerator {

    private static final int EAN_LENGTH = 13;

    /**
     * Address properties.
     */
    public static class AddressProperties {
        public static final String ADDRESS_LINE_1 = "line1";
        public static final String ADDRESS_LINE_2 = "line2";
        public static final String ADDRESS_LINE_3 = "line3";
        public static final String CITY = "city";
        public static final String COUNTRY = "NO";
        public static final String FIRST_NAME = "firstName";
        public static final String LAST_NAME = "lastName";
        public static final String HOUSE_NUMBER = "houseNumber";
        public static final String HOUSE_NUMBER_EXTENDED = "houseNumberExtended";
        public static final String PHONE_NUMBER = "phoneNumber";
        public static final String ZIPCODE = "zipcode";
        public static final String STORE_ID = "123456";
    }

    /**
     * CustomerInformation properties.
     */
    public static class CustomerInformationProperties {
        public static final String EMAIL_ADDRESS = "<EMAIL>";
        public static final String EXTERNAL_CUSTOMER_NUMBER = "externalCustomerNumber";
        public static final String CUSTOMER_ID = "customerId";
    }

    /**
     * OrderDetails properties.
     */
    public static class OrderDetailsProperties {
        public static final String EXTERNAL_ORDER_NUMBER = "extOrderNumber";
        public static final String CARRIER = "carrier";
        public static final String ORDER_TYPE = "orderType";
        public static final BigDecimal ORDER_VALUE = BigDecimal.TEN;
        public static final BigDecimal SHIPPING_FEES = BigDecimal.ZERO;
        public static final BigDecimal SHIPPING_FEES_TAX_PERCENTAGE = BigDecimal.ZERO;
        public static final String SHIPPING_METHOD = "shippingMethod";
        public static final ZonedDateTime ORDER_CREATION_DATE = ZonedDateTime.now();
        public static final String CARRIER_VARIANT = "carrierVariant";
        public static final BigDecimal PAYMENT_FEES = BigDecimal.valueOf(10.85);
        public static final Boolean SHIPPING_FEES_CANCELLED = Boolean.FALSE;
    }

    /**
     * First OrderLine properties.
     */
    public static class FirstOrderLineProperties {
        public static final String EAN = "ean1";
        public static final String PRODUCT_NAME = "productName1";
        public static final int LINE_NUMBER = 1;
        public static final int QUANTITY = 10;
        public static final BigDecimal RETAIL_PRICE = BigDecimal.ONE;
        public static final BigDecimal DISCOUNT_VALUE = BigDecimal.ZERO;
        public static final BigDecimal TAX_PERCENTAGE = BigDecimal.ZERO;
        public static final boolean IS_GIFT_ITEM = false;
        public static final String PARTNER_REFERENCE = "partnerReference";
        public static final String BRAND = "vero-moda";
    }

    /**
     * Second OrderLine properties.
     */
    public static class SecondOrderLineProperties {
        public static final String EAN = "ean2";
    }

    /**
     * Payment properties.
     */
    public static class PaymentProperties {
        public static final String PAYMENT_TYPE_1 = "paymentType1";
        public static final String PAYMENT_SUB_METHOD_1 = "paymentSubMethod1";
        public static final String PAYMENT_TYPE_2 = "paymentType2";
        public static final String PAYMENT_SUB_METHOD_2 = "paymentSubMethod2";
    }

    /**
     * Order properties.
     */
    public static class OrderProperties {
        public static final boolean IS_TEST = false;
        public static final ZonedDateTime PLACED_DATE = ZonedDateTime.now();
        public static final String MARKET_PLACE = "marketPlace";
        public static final String STORE = "store";
        public static final String CHANNEL = "channel";
        public static final String BRAND = "brand";
        public static final String ACTION_CODE = "actionCode";
    }

    /**
     * ShippingInformation properties.
     */
    public static class ShippingInformationProperties {
        public static final String PARCEL_LOCKER = "A Locker somewhere";
        public static final Boolean IS_RETURN_TRACKING_NUMBER_REQUIRED = Boolean.TRUE;
        public static final Set<AdditionalInformation> ADDITIONAL_INFORMATION_SET = new HashSet<>(Arrays.asList(
                new AdditionalInformation().withKey("key1").withValue("value")
        ));
    }

    /**
     * Create full OFF.
     * @return OFF
     */
    public static OrderForFulfillment createFullOrderForFulfillmentMessage() {
        final String orderId = UUID.randomUUID().toString();

        final Address billingAddress = new Address();
        billingAddress.withAddressLine1(AddressProperties.ADDRESS_LINE_1)
                .withAddressLine2(AddressProperties.ADDRESS_LINE_2)
                .withAddressLine3(AddressProperties.ADDRESS_LINE_3)
                .withCity(AddressProperties.CITY)
                .withCountry(AddressProperties.COUNTRY)
                .withFirstName(AddressProperties.FIRST_NAME)
                .withLastName(AddressProperties.LAST_NAME)
                .withHouseNumber(AddressProperties.HOUSE_NUMBER)
                .withHouseNumberExtended(AddressProperties.HOUSE_NUMBER_EXTENDED)
                .withPhoneNumber(AddressProperties.PHONE_NUMBER)
                .withZipcode(AddressProperties.ZIPCODE)
                .withStoreId(AddressProperties.STORE_ID);

        final Address shippingAddress = new Address();
        shippingAddress.withAddressLine1(AddressProperties.ADDRESS_LINE_1)
                .withAddressLine2(AddressProperties.ADDRESS_LINE_2)
                .withAddressLine3(AddressProperties.ADDRESS_LINE_3)
                .withCity(AddressProperties.CITY)
                .withCountry(AddressProperties.COUNTRY)
                .withFirstName(AddressProperties.FIRST_NAME)
                .withLastName(AddressProperties.LAST_NAME)
                .withHouseNumber(AddressProperties.HOUSE_NUMBER)
                .withHouseNumberExtended(AddressProperties.HOUSE_NUMBER_EXTENDED)
                .withPhoneNumber(AddressProperties.PHONE_NUMBER)
                .withZipcode(AddressProperties.ZIPCODE)
                .withStoreId(AddressProperties.STORE_ID);

        final CustomerInformation customerInformation = new CustomerInformation()
                .withEmail(CustomerInformationProperties.EMAIL_ADDRESS)
                .withExternalCustomerNumber(CustomerInformationProperties.EXTERNAL_CUSTOMER_NUMBER)
                .withBillingAddress(billingAddress)
                .withCustomerId(CustomerInformationProperties.CUSTOMER_ID);

        final OrderDetails orderDetails = new OrderDetails()
                .withCarrier(OrderDetailsProperties.CARRIER)
                .withExternalOrderNumber(OrderDetailsProperties.EXTERNAL_ORDER_NUMBER)
                .withOrderCreationDate(OrderDetailsProperties.ORDER_CREATION_DATE)
                .withOrderType(OrderDetailsProperties.ORDER_TYPE)
                .withOrderValue(OrderDetailsProperties.ORDER_VALUE)
                .withShippingFees(OrderDetailsProperties.SHIPPING_FEES)
                .withShippingFeesTaxPercentage(OrderDetailsProperties.SHIPPING_FEES_TAX_PERCENTAGE)
                .withShippingMethod(OrderDetailsProperties.SHIPPING_METHOD)
                .withCarrierVariant(OrderDetailsProperties.CARRIER_VARIANT)
                .withShippingFeesCancelled(OrderDetailsProperties.SHIPPING_FEES_CANCELLED);

        final ShippingInformation shippingInformation = new ShippingInformation();
        shippingInformation.withShippingAddress(shippingAddress);
        shippingInformation.withParcelLocker(ShippingInformationProperties.PARCEL_LOCKER);
        shippingInformation
            .withIsReturnTrackingNumberRequired(ShippingInformationProperties.IS_RETURN_TRACKING_NUMBER_REQUIRED);
        shippingInformation.withAdditionalInformation(ShippingInformationProperties.ADDITIONAL_INFORMATION_SET);

        final List<OrderLine> orderLines = Collections.singletonList(new OrderLine()
                .withEan(FirstOrderLineProperties.EAN)
                .withProductName(FirstOrderLineProperties.PRODUCT_NAME)
                .withLineNumber(FirstOrderLineProperties.LINE_NUMBER)
                .withQuantity(FirstOrderLineProperties.QUANTITY)
                .withRetailPrice(FirstOrderLineProperties.RETAIL_PRICE)
                .withDiscountValue(FirstOrderLineProperties.DISCOUNT_VALUE)
                .withTaxPercentage(FirstOrderLineProperties.TAX_PERCENTAGE)
                .withIsGiftItem(FirstOrderLineProperties.IS_GIFT_ITEM)
                .withPartnerReference(FirstOrderLineProperties.PARTNER_REFERENCE));

        List<Payment> payments = Arrays.asList(
                new Payment()
                        .withName(PaymentProperties.PAYMENT_TYPE_1)
                        .withSubMethod(PaymentProperties.PAYMENT_SUB_METHOD_1),
                new Payment()
                        .withName(PaymentProperties.PAYMENT_TYPE_2)
                        .withSubMethod(PaymentProperties.PAYMENT_SUB_METHOD_2)
        );

        return new OrderForFulfillment()
                .withOrderId(orderId)
                .withPlacedDate(OrderProperties.PLACED_DATE)
                .withIsTest(OrderProperties.IS_TEST)
                .withCustomerInformation(customerInformation)
                .withOrderDetails(orderDetails)
                .withShippingInformation(shippingInformation)
                .withPayments(payments)
                .withOrderLines(orderLines)
                .withMarketPlace(OrderProperties.MARKET_PLACE)
                .withStore(OrderProperties.STORE)
                .withChannel(OrderProperties.CHANNEL)
                .withBrand(OrderProperties.BRAND)
                .withActionCode(OrderProperties.ACTION_CODE);
    }

    /**
     * Create min valid OFF.
     * @return OFF
     */
    public static OrderForFulfillment createMinValidOrderForFulfillmentMessage() {
        final String orderId = UUID.randomUUID().toString();

        final Address billingAddress = new Address()
                .withAddressLine1(AddressProperties.ADDRESS_LINE_1)
                .withCity(AddressProperties.CITY)
                .withLastName(AddressProperties.LAST_NAME);

        final Address shippingAddress = new Address()
                .withAddressLine1(AddressProperties.ADDRESS_LINE_1)
                .withCity(AddressProperties.CITY)
                .withLastName(AddressProperties.LAST_NAME)
                .withCountry(AddressProperties.COUNTRY);

        final CustomerInformation customerInformation = new CustomerInformation()
                .withBillingAddress(billingAddress);

        final ShippingInformation shippingInformation = new ShippingInformation()
                .withShippingAddress(shippingAddress);

        final List<OrderLine> orderLines = Collections.singletonList(new OrderLine()
                .withEan(FirstOrderLineProperties.EAN)
                .withLineNumber(FirstOrderLineProperties.LINE_NUMBER)
                .withRetailPrice(FirstOrderLineProperties.RETAIL_PRICE)
                .withDiscountValue(FirstOrderLineProperties.DISCOUNT_VALUE)
                .withQuantity(FirstOrderLineProperties.QUANTITY)
                .withTaxPercentage(FirstOrderLineProperties.TAX_PERCENTAGE)
                .withBrand(FirstOrderLineProperties.BRAND));

        return new OrderForFulfillment()
                .withOrderId(orderId)
                .withCustomerInformation(customerInformation)
                .withShippingInformation(shippingInformation)
                .withOrderLines(orderLines)
                .withChannel(OrderProperties.CHANNEL)
                .withIsTest(true)
                .withPlacedDate(ZonedDateTime.of(1, 1, 1, 0, 0, 0, 0, ZoneId.of("UTC")));
    }

    /**
     * Create <i>numberOfLines</i> with random EANs for testing.
     * @param numberOfLines number of lines to be created.
     * @return list of order lines with random eans.
     */
    public static List<OrderLine> createOrderLines(int numberOfLines) {
        final List<OrderLine> orderLines = new ArrayList<>();
        for (int i = 1; i <= numberOfLines; i++) {
            orderLines.add(new OrderLine()
                    .withEan(RandomStringUtils.randomAlphabetic(EAN_LENGTH))
                    .withProductName(FirstOrderLineProperties.PRODUCT_NAME)
                    .withLineNumber(i)
                    .withQuantity(FirstOrderLineProperties.QUANTITY)
                    .withRetailPrice(FirstOrderLineProperties.RETAIL_PRICE)
                    .withDiscountValue(FirstOrderLineProperties.DISCOUNT_VALUE)
                    .withTaxPercentage(FirstOrderLineProperties.TAX_PERCENTAGE)
                    .withIsGiftItem(FirstOrderLineProperties.IS_GIFT_ITEM)
                    .withPartnerReference(FirstOrderLineProperties.PARTNER_REFERENCE));
        }

        return orderLines;
    }
}
